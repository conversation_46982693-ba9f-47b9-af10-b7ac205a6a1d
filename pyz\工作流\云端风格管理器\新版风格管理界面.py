# -*- coding: utf-8 -*-
"""
新版云端风格管理界面 - 图生图模式
支持性别分类和年龄段的风格管理界面
"""

import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from .风格数据管理 import 风格数据管理器


class SquareImageLabel(QLabel):
    """自定义正方形图片标签 - 根据宽度自动调整高度保持正方形"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumSize(250, 250)  # 增大最小尺寸
        self._aspect_ratio = 1.0  # 正方形比例
        self._update_timer = QTimer()
        self._update_timer.setSingleShot(True)
        self._update_timer.timeout.connect(self._update_square_size)

    def resizeEvent(self, event):
        """重写resize事件，保持正方形比例"""
        super().resizeEvent(event)
        # 延迟更新，避免频繁调用
        self._update_timer.start(50)

    def _update_square_size(self):
        """更新为正方形尺寸"""
        width = self.width()
        if width > 0:
            new_height = int(width * self._aspect_ratio)
            if new_height != self.height():
                self.setFixedHeight(new_height)
                self.update()

    def sizeHint(self):
        """返回建议尺寸"""
        width = 200  # 默认宽度
        return QSize(width, width)

    def heightForWidth(self, width):
        """根据宽度返回对应的高度"""
        return int(width * self._aspect_ratio)


class 性别选择组件(QWidget):
    """性别选择单选框组件"""
    
    gender_changed = pyqtSignal(str)  # 性别改变信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(15)
        
        # 性别单选框组
        self.gender_group = QButtonGroup(self)
        
        self.male_radio = QRadioButton("男性")
        self.male_radio.setChecked(True)  # 默认选中男性
        self.male_radio.setStyleSheet("""
            QRadioButton {
                font-size: 18px;
                font-weight: bold;
                color: #495057;
                spacing: 12px;
            }
            QRadioButton::indicator {
                width: 22px;
                height: 22px;
            }
            QRadioButton::indicator:unchecked {
                border: 3px solid #6c757d;
                border-radius: 11px;
                background-color: white;
            }
            QRadioButton::indicator:checked {
                border: 3px solid #007ACC;
                border-radius: 11px;
                background-color: #007ACC;
                background: qradialgradient(cx:0.5, cy:0.5, radius:0.5,
                    stop:0 #007ACC, stop:0.6 #007ACC, stop:0.7 white, stop:1 white);
            }
            QRadioButton::indicator:unchecked:hover {
                border: 3px solid #4a90e2;
                background-color: #f0f8ff;
            }
            QRadioButton::indicator:checked:hover {
                border: 3px solid #005A9E;
                background: qradialgradient(cx:0.5, cy:0.5, radius:0.5,
                    stop:0 #005A9E, stop:0.6 #005A9E, stop:0.7 white, stop:1 white);
            }
        """)
        
        self.female_radio = QRadioButton("女性")
        self.female_radio.setStyleSheet(self.male_radio.styleSheet())
        
        self.gender_group.addButton(self.male_radio, 0)
        self.gender_group.addButton(self.female_radio, 1)
        
        layout.addWidget(self.male_radio)
        layout.addWidget(self.female_radio)
        layout.addStretch()
        
        # 连接信号
        self.male_radio.toggled.connect(self.on_gender_changed)
        self.female_radio.toggled.connect(self.on_gender_changed)
    
    def on_gender_changed(self):
        """性别改变事件"""
        if self.male_radio.isChecked():
            self.gender_changed.emit("男性")
        elif self.female_radio.isChecked():
            self.gender_changed.emit("女性")
    
    def get_selected_gender(self) -> str:
        """获取当前选中的性别"""
        return "男性" if self.male_radio.isChecked() else "女性"
    
    def set_gender(self, gender: str):
        """设置性别"""
        if gender == "男性":
            self.male_radio.setChecked(True)
        elif gender == "女性":
            self.female_radio.setChecked(True)


class 风格导航栏(QWidget):
    """左侧风格导航栏组件"""
    
    style_selected = pyqtSignal(str)  # 风格选择信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.data_manager = 风格数据管理器()
        self.current_category = None
        self.current_style_id = None
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        self.setFixedWidth(250)
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border-right: 2px solid #dee2e6;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)
        
        # 标题
        title_label = QLabel("🎨 风格列表")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #495057;
                padding: 12px 0;
                border-bottom: 2px solid #dee2e6;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # 风格列表
        self.style_list = QListWidget()
        self.style_list.setContextMenuPolicy(Qt.CustomContextMenu)  # 启用自定义右键菜单
        self.style_list.customContextMenuRequested.connect(self.show_context_menu)  # 连接右键菜单信号
        self.style_list.setStyleSheet("""
            QListWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                font-size: 18px;
                outline: none;
            }
            QListWidget::item {
                padding: 18px 20px;
                border-bottom: 1px solid #f1f3f4;
                color: #495057;
            }
            QListWidget::item:hover {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QListWidget::item:selected {
                background-color: #007ACC;
                color: white;
                border: 2px solid #005A9E;
                font-weight: bold;
            }
        """)
        self.style_list.itemClicked.connect(self.on_style_item_clicked)
        layout.addWidget(self.style_list)
    
    def load_styles(self, category: str = None):
        """加载风格列表"""
        self.current_category = category
        self.style_list.clear()
        
        if category:
            styles = self.data_manager.get_category_styles(category)
        else:
            styles = self.data_manager.get_all_styles()
        
        for style in styles:
            item = QListWidgetItem(style["name"])
            item.setData(Qt.UserRole, style["id"])
            item.setToolTip(f"描述: {style.get('description', '无描述')}")
            self.style_list.addItem(item)
    
    def on_style_item_clicked(self, item):
        """风格项点击事件"""
        style_id = item.data(Qt.UserRole)
        if style_id:
            self.current_style_id = style_id
            print(f"🖱️ 风格导航栏点击: {item.text()} (ID: {style_id})")
            self.style_selected.emit(style_id)
            print(f"📤 已发射 style_selected 信号: {style_id}")
    
    def set_selected_style(self, style_id: str):
        """设置选中的风格"""
        self.current_style_id = style_id
        for i in range(self.style_list.count()):
            item = self.style_list.item(i)
            if item.data(Qt.UserRole) == style_id:
                self.style_list.setCurrentItem(item)
                break

    def show_context_menu(self, position):
        """显示右键菜单"""
        item = self.style_list.itemAt(position)
        if not item:
            return

        # 获取选中的风格ID
        style_id = item.data(Qt.UserRole)
        if not style_id:
            return

        # 创建右键菜单
        context_menu = QMenu(self)

        # 删除风格动作
        delete_action = QAction("🗑️ 删除风格", self)
        delete_action.setToolTip("删除此风格及其所有图片")
        delete_action.triggered.connect(lambda: self.delete_style(style_id))
        context_menu.addAction(delete_action)

        # 显示菜单
        context_menu.exec_(self.style_list.mapToGlobal(position))

    def delete_style(self, style_id: str):
        """删除风格及其所有图片"""
        try:
            # 获取风格信息
            style, category = self.data_manager._find_style_by_id(style_id)
            if not style:
                QMessageBox.warning(self, "警告", "未找到指定的风格！")
                return

            # 确认删除 - 使用简单的标准对话框
            import ctypes
            from ctypes import wintypes

            # 使用Windows原生消息框，确保按钮可见
            message = f"确定要删除风格 '{style['name']}' 吗？\n\n这将删除该风格的所有图片（男性/女性 x 4个年龄段）。\n此操作不可撤销！"

            # MB_YESNO = 4, MB_ICONQUESTION = 32, MB_DEFBUTTON2 = 256
            result = ctypes.windll.user32.MessageBoxW(
                0,  # 父窗口句柄
                message,  # 消息文本
                "确认删除",  # 标题
                4 | 32 | 256  # MB_YESNO | MB_ICONQUESTION | MB_DEFBUTTON2
            )

            # IDYES = 6
            reply = (result == 6)

            if reply:
                # 删除风格及其所有图片
                success = self.data_manager.delete_style(style_id)
                if success:
                    # 使用Windows原生消息框显示成功信息
                    import ctypes
                    ctypes.windll.user32.MessageBoxW(
                        0,
                        f"风格 '{style['name']}' 已成功删除！",
                        "成功",
                        0 | 64  # MB_OK | MB_ICONINFORMATION
                    )
                    # 刷新风格列表和表格内容
                    self.load_styles(self.current_category)
                    # 通知父窗口刷新表格内容
                    if hasattr(self.parent(), 'on_category_changed'):
                        self.parent().on_category_changed(self.current_category)
                    # 发送风格选择信号（清空选择）
                    self.style_selected.emit("")
                else:
                    QMessageBox.critical(self, "错误", "删除风格失败！")

        except Exception as e:
            print(f"❌ 删除风格失败: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"删除风格时发生错误：{str(e)}")


class 年龄段图片网格(QWidget):
    """年龄段图片网格组件 - 支持多风格表格显示"""

    image_clicked = pyqtSignal(str, str, str)  # 风格ID, 性别, 年龄段

    def __init__(self, parent=None):
        super().__init__(parent)
        self.data_manager = 风格数据管理器()
        self.current_style_id = None
        self.current_gender = "男性"
        self.current_category = None
        self.style_row_widgets = {}  # 存储风格ID到行widget的映射，用于锚点定位
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 年龄段标题行 - 平均分配宽度
        header_layout = QHBoxLayout()
        header_layout.setSpacing(10)

        age_groups = self.data_manager.get_age_groups()
        for age_group in age_groups:
            header_label = QLabel(age_group)
            header_label.setAlignment(Qt.AlignCenter)
            header_label.setStyleSheet("""
                QLabel {
                    font-size: 22px;
                    font-weight: bold;
                    color: #495057;
                    padding: 18px;
                    background-color: #e9ecef;
                    border-radius: 8px;
                    border: 2px solid #dee2e6;
                }
            """)
            header_layout.addWidget(header_label, 1)  # stretch=1 实现平均分配
        
        layout.addLayout(header_layout)
        
        # 图片网格区域 - 使用QTableWidget实现
        self.grid_scroll = QScrollArea()
        self.grid_scroll.setWidgetResizable(True)
        self.grid_scroll.setStyleSheet("""
            QScrollArea {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                background-color: white;
            }
        """)

        # 创建表格组件
        self.grid_table = QTableWidget()
        self.grid_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                selection-background-color: #e3f2fd;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 10px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                font-size: 16px;
            }
        """)

        self.grid_scroll.setWidget(self.grid_table)
        layout.addWidget(self.grid_scroll)
    
    def load_style_images(self, style_id: str, gender: str):
        """加载指定风格和性别的图片 - 单风格模式（保持兼容性）"""
        self.current_style_id = style_id
        self.current_gender = gender

        # 清空现有内容
        self.clear_grid()

        if not style_id:
            return

        # 获取风格信息
        style, _ = self.data_manager._find_style_by_id(style_id)
        if not style:
            return

        # 创建图片行
        self.create_image_row(style, gender)

    def load_all_styles(self, category: str, gender: str):
        """加载所有风格的图片 - 使用表格模式实现真正的平均分配"""
        self.current_category = category
        self.current_gender = gender
        self.style_row_widgets.clear()

        # 清空现有表格内容
        self.grid_table.clear()

        # 设置表格列 - 只有4个年龄段，不需要风格名称列（左边导航栏已有）
        self.grid_table.setColumnCount(4)  # 只要4个年龄段
        self.grid_table.setHorizontalHeaderLabels(['儿童', '青年', '中年', '老年'])

        # 获取指定分类的所有风格
        styles = self.data_manager.get_styles_by_category(category)

        if not styles:
            # 显示空状态
            self.grid_table.setRowCount(1)
            empty_item = QTableWidgetItem("📝 当前分类下暂无风格，请先添加风格")
            empty_item.setTextAlignment(Qt.AlignCenter)
            self.grid_table.setItem(0, 0, empty_item)
            self.grid_table.setSpan(0, 0, 1, 4)  # 合并4列
            return

        # 设置表格行数
        self.grid_table.setRowCount(len(styles))

        # 为每个风格创建表格行
        for row, style in enumerate(styles):
            self.create_table_row(row, style, gender)
            # 存储风格ID到行号的映射，用于锚点定位
            self.style_row_widgets[style["id"]] = row

        # 设置表格属性 - 实现真正的平均分配
        header = self.grid_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)  # 所有列平均分配宽度
        self.grid_table.verticalHeader().setVisible(False)
        self.grid_table.setAlternatingRowColors(True)
        self.grid_table.setSelectionBehavior(QAbstractItemView.SelectRows)

        # 设置行高与列宽相等，形成正方形单元格
        # 计算单元格宽度（总宽度除以4列）
        # 使用更长的延迟确保表格完全渲染
        QTimer.singleShot(200, self.adjust_row_height_to_match_width)

        print(f"✅ 加载完成: {len(styles)} 个风格，映射关系: {self.style_row_widgets}")

        # 强制更新表格显示
        self.grid_table.update()
        self.grid_table.repaint()

    def adjust_row_height_to_match_width(self):
        """调整行高与列宽相等，形成正方形单元格"""
        if not hasattr(self, 'grid_table') or not self.grid_table:
            print(f"❌ 表格未初始化，无法调整行高")
            return

        if self.grid_table.columnCount() <= 0:
            print(f"❌ 表格列数为0，无法调整行高")
            return

        try:
            # 获取表格总宽度
            table_width = self.grid_table.viewport().width()
            if table_width <= 0:
                print(f"❌ 表格宽度无效: {table_width}")
                return

            # 计算单列宽度（平均分配），但限制最大高度
            column_width = max(200, min(400, table_width // 4))  # 最小200px，最大400px
            row_count = self.grid_table.rowCount()

            print(f"📏 调整表格行高: 表格宽度={table_width}, 列宽={column_width}, 行数={row_count}")

            # 设置所有行的高度等于列宽
            for i in range(row_count):
                self.grid_table.setRowHeight(i, column_width)

            print(f"✅ 行高调整完成: 每行高度={column_width}px")

        except Exception as e:
            print(f"❌ 调整行高失败: {e}")
            import traceback
            traceback.print_exc()

    def create_table_row(self, row: int, style: dict, gender: str):
        """创建表格行 - 只有4个年龄段图片（风格名称在左侧导航栏）"""
        # 4列：年龄段图片
        age_groups = self.data_manager.get_age_groups()
        for col, age_group in enumerate(age_groups):
            # 创建图片标签
            image_label = QLabel()
            image_label.setAlignment(Qt.AlignCenter)
            image_label.setScaledContents(True)
            image_label.setStyleSheet("""
                QLabel {
                    border: 2px solid #dee2e6;
                    border-radius: 8px;
                    background-color: #f8f9fa;
                    margin: 5px;
                }
                QLabel:hover {
                    border: 3px solid #007ACC;
                    background-color: #e3f2fd;
                }
            """)

            # 加载图片
            image_filename = self.data_manager.get_style_image(style["id"], gender, age_group)
            has_image = False
            if image_filename:
                image_path = self.data_manager.get_image_path(image_filename)
                if os.path.exists(image_path):
                    pixmap = QPixmap(image_path)
                    if not pixmap.isNull():
                        # 缩放图片保持比例
                        scaled_pixmap = pixmap.scaled(300, 300, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                        image_label.setPixmap(scaled_pixmap)
                        has_image = True
                    else:
                        self.set_placeholder_image(image_label, f"加载失败\n{age_group}")
                else:
                    self.set_placeholder_image(image_label, f"文件不存在\n{age_group}")
            else:
                self.set_placeholder_image(image_label, f"点击上传\n{age_group}")

            # 添加双击事件 - 区分有图片和无图片的情况
            if has_image:
                image_label.mouseDoubleClickEvent = lambda event, s=style["id"], g=gender, a=age_group: self.on_image_clicked(s, g, a)
            else:
                image_label.mouseDoubleClickEvent = lambda event, s=style["id"], g=gender, a=age_group: self.upload_image(s, g, a)

            # 设置到表格单元格
            self.grid_table.setCellWidget(row, col, image_label)

    def clear_grid(self):
        """清空网格内容"""
        if hasattr(self, 'grid_table'):
            self.grid_table.clear()
            self.grid_table.setRowCount(0)
    
    def create_image_row(self, style: dict, gender: str):
        """创建图片行 - 单风格模式"""
        row_widget = self.create_style_row(style, gender)
        self.grid_layout.addWidget(row_widget)

    def create_style_row(self, style: dict, gender: str):
        """创建风格行 - 包含风格名称和4张年龄段图片"""
        # 主容器
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)

        # 风格名称标题
        style_title = QLabel(style.get("name", "未命名风格"))
        style_title.setAlignment(Qt.AlignLeft)
        style_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 8px 12px;
                background-color: #f8f9fa;
                border-left: 4px solid #007bff;
                border-radius: 4px;
                margin-bottom: 5px;
            }
        """)
        main_layout.addWidget(style_title)

        # 图片行 - 使用平均分配的布局
        row_layout = QHBoxLayout()
        row_layout.setSpacing(10)  # 减少间距以留更多空间给图片

        age_groups = self.data_manager.get_age_groups()

        for age_group in age_groups:
            # 创建图片容器 - 每个容器平均分配宽度
            image_container = self.create_image_container(style, gender, age_group)
            row_layout.addWidget(image_container, 1)  # stretch=1 实现平均分配

        main_layout.addLayout(row_layout)

        # 设置主容器样式
        main_widget.setStyleSheet("""
            QWidget {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 12px;
                margin: 8px 0;
            }
            QWidget:hover {
                border-color: #007bff;
                background-color: #f0f8ff;
            }
        """)

        return main_widget

    def show_empty_state(self):
        """显示空状态"""
        empty_widget = QWidget()
        empty_layout = QVBoxLayout(empty_widget)
        empty_layout.setAlignment(Qt.AlignCenter)

        empty_label = QLabel("📂 当前分类暂无风格")
        empty_label.setAlignment(Qt.AlignCenter)
        empty_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #6c757d;
                padding: 40px;
            }
        """)
        empty_layout.addWidget(empty_label)

        self.grid_layout.addWidget(empty_widget)

    def scroll_to_style(self, style_id: str):
        """滚动到指定风格位置 - 表格锚点定位功能"""
        if style_id not in self.style_row_widgets:
            print(f"❌ 风格ID {style_id} 不在映射中")
            print(f"🔍 当前映射: {list(self.style_row_widgets.keys())}")
            return

        target_row = self.style_row_widgets[style_id]
        print(f"🎯 滚动到行: {target_row}")

        # 确保表格存在且已初始化
        if not hasattr(self, 'grid_table') or not self.grid_table:
            print(f"❌ 表格未初始化")
            return

        # 确保表格有足够的行
        if target_row >= self.grid_table.rowCount():
            print(f"❌ 目标行 {target_row} 超出表格行数 {self.grid_table.rowCount()}")
            return

        print(f"📊 表格信息: 行数={self.grid_table.rowCount()}, 列数={self.grid_table.columnCount()}")

        # 先确保行高已正确设置，然后再滚动
        self._ensure_row_heights_set()

        # 使用QTimer延迟执行滚动，确保表格完全加载和渲染
        QTimer.singleShot(500, lambda: self._perform_scroll_to_row(target_row))

    def _ensure_row_heights_set(self):
        """确保表格行高已正确设置"""
        if not hasattr(self, 'grid_table') or not self.grid_table:
            return

        # 检查是否有行高为0的情况
        needs_adjustment = False
        for i in range(self.grid_table.rowCount()):
            if self.grid_table.rowHeight(i) <= 0:
                needs_adjustment = True
                break

        if needs_adjustment:
            print(f"🔧 检测到行高未设置，立即调整...")
            self.adjust_row_height_to_match_width()
        else:
            print(f"✅ 行高已正确设置")

    def _perform_scroll_to_row(self, target_row: int):
        """执行实际的滚动操作"""
        try:
            # 方法1: 尝试使用QTableWidget的scrollTo方法
            print(f"🔄 方法1: 使用scrollTo方法滚动到行 {target_row}")

            # 创建一个临时的QModelIndex来定位目标行
            model_index = self.grid_table.model().index(target_row, 0)
            if model_index.isValid():
                # 使用动画滚动效果
                self._animate_scroll_to_position(target_row)
                print(f"✅ 使用动画滚动方法成功")
                success = True
            else:
                print(f"❌ 无效的模型索引")
                success = False

            if not success:
                # 方法2: 使用垂直滚动条直接计算
                print(f"🔄 方法2: 使用滚动条计算位置滚动到行 {target_row}")

                v_scrollbar = self.grid_table.verticalScrollBar()
                if not v_scrollbar:
                    print(f"❌ 无法获取垂直滚动条")
                    return

                # 获取滚动条的当前状态
                current_value = v_scrollbar.value()
                min_value = v_scrollbar.minimum()
                max_value = v_scrollbar.maximum()

                print(f"📊 滚动条状态: 当前={current_value}, 最小={min_value}, 最大={max_value}")

                # 计算目标位置 - 使用累积行高
                total_height = 0
                for i in range(target_row):
                    row_height = self.grid_table.rowHeight(i)
                    if row_height <= 0:
                        print(f"⚠️ 行 {i} 高度为 {row_height}，使用默认高度200px")
                        row_height = 200
                    total_height += row_height
                    print(f"📏 行 {i} 高度: {row_height}px, 累积: {total_height}px")

                print(f"📏 计算的滚动位置: {total_height}px")

                # 确保滚动位置在有效范围内
                scroll_position = max(min_value, min(total_height, max_value))
                print(f"📏 实际滚动位置: {scroll_position}px (限制在 {min_value}-{max_value} 范围内)")

                # 执行滚动
                v_scrollbar.setValue(scroll_position)

                # 验证滚动是否成功
                new_value = v_scrollbar.value()
                print(f"📊 滚动后位置: {new_value}px")

                if abs(new_value - scroll_position) > 10:  # 允许10px的误差
                    print(f"⚠️ 滚动位置不准确，期望: {scroll_position}px, 实际: {new_value}px")
                else:
                    print(f"✅ 滚动位置准确")

            # 高亮显示目标行
            self.highlight_table_row(target_row)
            print(f"✅ 滚动完成，已高亮行 {target_row}")

        except Exception as e:
            print(f"❌ 滚动执行失败: {e}")
            import traceback
            traceback.print_exc()

    def _animate_scroll_to_position(self, target_row: int):
        """使用动画效果滚动到指定位置"""
        try:
            from PyQt5.QtCore import QPropertyAnimation, QEasingCurve

            v_scrollbar = self.grid_table.verticalScrollBar()
            if not v_scrollbar:
                return

            # 计算目标位置
            total_height = 0
            for i in range(target_row):
                row_height = self.grid_table.rowHeight(i)
                if row_height <= 0:
                    row_height = 300  # 使用合理的默认高度
                total_height += row_height

            # 限制滚动位置在有效范围内
            target_position = max(v_scrollbar.minimum(),
                                min(total_height, v_scrollbar.maximum()))

            print(f"🎬 开始动画滚动: 从 {v_scrollbar.value()} 到 {target_position}")

            # 创建滚动动画
            self.scroll_animation = QPropertyAnimation(v_scrollbar, b"value")
            self.scroll_animation.setDuration(800)  # 800ms动画时间
            self.scroll_animation.setStartValue(v_scrollbar.value())
            self.scroll_animation.setEndValue(target_position)
            self.scroll_animation.setEasingCurve(QEasingCurve.OutCubic)  # 缓出动画

            # 动画完成后的回调
            def on_animation_finished():
                print(f"✅ 动画滚动完成: 最终位置 {v_scrollbar.value()}")

            self.scroll_animation.finished.connect(on_animation_finished)
            self.scroll_animation.start()

        except ImportError:
            print(f"⚠️ 动画库不可用，使用直接滚动")
            # 回退到直接滚动
            model_index = self.grid_table.model().index(target_row, 0)
            self.grid_table.scrollTo(model_index, QAbstractItemView.PositionAtTop)
        except Exception as e:
            print(f"❌ 动画滚动失败: {e}")
            # 回退到直接滚动
            model_index = self.grid_table.model().index(target_row, 0)
            self.grid_table.scrollTo(model_index, QAbstractItemView.PositionAtTop)

    def highlight_table_row(self, row: int):
        """为表格行添加高亮边框"""
        if not hasattr(self, 'grid_table'):
            return

        # 清除之前的高亮
        self.clear_table_highlights()

        # 为指定行的所有单元格添加高亮边框
        for col in range(self.grid_table.columnCount()):
            widget = self.grid_table.cellWidget(row, col)
            if widget:
                widget.setStyleSheet("""
                    QLabel {
                        border: 4px solid #007ACC;
                        border-radius: 8px;
                        background-color: #e3f2fd;
                        margin: 5px;
                    }
                """)

        # 记录当前高亮的行
        self.highlighted_row = row

    def clear_table_highlights(self):
        """清除表格中的所有高亮"""
        if not hasattr(self, 'grid_table'):
            return

        # 恢复所有单元格的默认样式
        for row in range(self.grid_table.rowCount()):
            for col in range(self.grid_table.columnCount()):
                widget = self.grid_table.cellWidget(row, col)
                if widget:
                    widget.setStyleSheet("""
                        QLabel {
                            border: 2px solid #dee2e6;
                            border-radius: 8px;
                            background-color: #f8f9fa;
                            margin: 5px;
                        }
                        QLabel:hover {
                            border: 3px solid #007ACC;
                            background-color: #e3f2fd;
                        }
                    """)

        # 清除高亮记录
        if hasattr(self, 'highlighted_row'):
            delattr(self, 'highlighted_row')

    def highlight_style_row(self, widget):
        """高亮显示风格行"""
        # 临时高亮效果
        original_style = widget.styleSheet()
        highlight_style = """
            QWidget {
                background-color: #fff3cd;
                border: 2px solid #ffc107;
                border-radius: 12px;
                margin: 8px 0;
            }
        """
        widget.setStyleSheet(highlight_style)

        # 1秒后恢复原样式
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(1000, lambda: widget.setStyleSheet(original_style))
    
    def create_image_container(self, style: dict, gender: str, age_group: str):
        """创建单个图片容器 - 支持平均分配宽度和正方形图片"""
        container = QFrame()
        # 移除固定尺寸，让容器可以平均分配宽度
        container.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        container.setMinimumWidth(300)  # 增大最小宽度
        container.setMinimumHeight(380)  # 增大最小高度以容纳更大图片
        container.setStyleSheet("""
            QFrame {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                background-color: #f8f9fa;
                margin: 2px;
            }
            QFrame:hover {
                border: 3px solid #007ACC;
                background-color: #e3f2fd;
            }
        """)

        layout = QVBoxLayout(container)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)

        # 图片标签 - 动态正方形，真正平均分配
        image_label = SquareImageLabel()  # 使用自定义的正方形图片标签
        image_label.setAlignment(Qt.AlignCenter)
        image_label.setScaledContents(True)
        # 让图片标签能够扩展并保持正方形
        image_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        # 使用定时器确保尺寸同步
        def update_square_size():
            if image_label.width() > 0:
                image_label.setFixedHeight(image_label.width())

        QTimer.singleShot(100, update_square_size)  # 延迟更新确保布局完成

        # 加载图片
        image_filename = self.data_manager.get_style_image(style["id"], gender, age_group)
        has_image = False
        if image_filename:
            image_path = self.data_manager.get_image_path(image_filename)
            if image_path and os.path.exists(image_path):
                pixmap = QPixmap(image_path)
                if not pixmap.isNull():
                    image_label.setPixmap(pixmap)
                    image_label.setStyleSheet("""
                        QLabel {
                            border: 1px solid #ccc;
                            border-radius: 4px;
                            background-color: white;
                        }
                    """)
                    has_image = True

        if not has_image:
            self.set_placeholder_image(image_label, "点击上传图片")

        layout.addWidget(image_label)

        # 按钮区域 - 垂直布局节省空间
        button_layout = QVBoxLayout()
        button_layout.setSpacing(3)

        # 上传按钮 - 调整尺寸适应新布局
        upload_btn = QPushButton("📤 上传")
        upload_btn.setFixedSize(80, 30)
        upload_btn.setToolTip("上传图片")
        upload_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12px;
                padding: 2px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        upload_btn.clicked.connect(lambda: self.upload_image(style["id"], gender, age_group))
        button_layout.addWidget(upload_btn)

        # 选择按钮（如果有图片）
        if has_image:
            select_btn = QPushButton("✅ 选择")
            select_btn.setFixedSize(80, 30)
            select_btn.setToolTip("选择此风格")
            select_btn.setStyleSheet("""
                QPushButton {
                    background-color: #007ACC;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 12px;
                    padding: 2px;
                }
                QPushButton:hover {
                    background-color: #005A9E;
                }
            """)
            select_btn.clicked.connect(lambda: self.on_image_clicked(style["id"], gender, age_group))
            button_layout.addWidget(select_btn)

        # 删除按钮（如果有图片）
        if has_image:
            delete_btn = QPushButton("🗑️ 删除")
            delete_btn.setFixedSize(80, 30)
            delete_btn.setToolTip("删除图片")
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 12px;
                    padding: 2px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)
            delete_btn.clicked.connect(lambda: self.delete_image(style["id"], gender, age_group))
            button_layout.addWidget(delete_btn)

        button_layout.addStretch()

        # 创建按钮容器
        button_widget = QWidget()
        button_widget.setLayout(button_layout)
        layout.addWidget(button_widget)

        # 添加双击事件（用于选择风格）
        if has_image:
            image_label.mouseDoubleClickEvent = lambda event: self.on_image_clicked(style["id"], gender, age_group)
        else:
            image_label.mouseDoubleClickEvent = lambda event: self.upload_image(style["id"], gender, age_group)

        # 确保容器显示后更新图片尺寸为正方形
        def ensure_square_image():
            if image_label.width() > 0 and image_label.height() != image_label.width():
                image_label.setFixedHeight(image_label.width())
                container.update()

        # 多次尝试确保尺寸同步
        QTimer.singleShot(200, ensure_square_image)
        QTimer.singleShot(500, ensure_square_image)
        QTimer.singleShot(1000, ensure_square_image)

        return container
    
    def set_placeholder_image(self, label: QLabel, text: str):
        """设置占位符图片"""
        label.setText(text)
        label.setStyleSheet("""
            QLabel {
                border: 2px dashed #ccc;
                border-radius: 4px;
                background-color: #f8f9fa;
                color: #6c757d;
                font-size: 14px;
            }
        """)
    
    def on_image_clicked(self, style_id: str, gender: str, age_group: str):
        """图片点击事件"""
        print(f"🖱️ 点击图片: 风格={style_id}, 性别={gender}, 年龄段={age_group}")
        self.image_clicked.emit(style_id, gender, age_group)

    def upload_image(self, style_id: str, gender: str, age_group: str):
        """上传图片"""
        print(f"📤 上传图片: 风格={style_id}, 性别={gender}, 年龄段={age_group}")

        # 打开文件选择对话框
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(
            self,
            f"选择{gender}{age_group}的风格图片",
            "",
            "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif);;所有文件 (*)"
        )

        if file_path:
            # 保存图片
            success = self.data_manager.set_style_image(style_id, gender, age_group, file_path)
            if success:
                print(f"✅ 图片上传成功: {file_path}")

                # 验证图片是否真的保存了
                saved_filename = self.data_manager.get_style_image(style_id, gender, age_group)
                print(f"🔍 验证保存的图片文件名: {saved_filename}")

                if saved_filename:
                    saved_path = self.data_manager.get_image_path(saved_filename)
                    print(f"🔍 验证保存的图片路径: {saved_path}")
                    if saved_path and os.path.exists(saved_path):
                        print(f"✅ 图片文件确实存在: {saved_path}")
                    else:
                        print(f"❌ 图片文件不存在: {saved_path}")

                # 刷新显示
                self.load_style_images(style_id, gender)
                # 使用Windows原生消息框显示成功信息
                import ctypes
                ctypes.windll.user32.MessageBoxW(
                    0,
                    "图片上传成功！",
                    "成功",
                    0 | 64  # MB_OK | MB_ICONINFORMATION
                )
            else:
                print(f"❌ 图片上传失败: {file_path}")
                QMessageBox.warning(self, "失败", "图片上传失败，请检查文件格式！")

    def delete_image(self, style_id: str, gender: str, age_group: str):
        """删除图片"""
        print(f"🗑️ 删除图片: 风格={style_id}, 性别={gender}, 年龄段={age_group}")

        # 确认删除 - 使用简单的标准对话框
        import ctypes
        from ctypes import wintypes

        # 使用Windows原生消息框，确保按钮可见
        message = f"确定要删除{gender}{age_group}的风格图片吗？"

        # MB_YESNO = 4, MB_ICONQUESTION = 32, MB_DEFBUTTON2 = 256
        result = ctypes.windll.user32.MessageBoxW(
            0,  # 父窗口句柄
            message,  # 消息文本
            "确认删除",  # 标题
            4 | 32 | 256  # MB_YESNO | MB_ICONQUESTION | MB_DEFBUTTON2
        )

        # IDYES = 6
        reply = (result == 6)

        if reply:
            # 获取当前图片文件名
            image_filename = self.data_manager.get_style_image(style_id, gender, age_group)
            if image_filename:
                # 删除文件
                self.data_manager._delete_style_image(image_filename)

                # 更新数据
                style, _ = self.data_manager._find_style_by_id(style_id)
                if style and "images" in style:
                    style["images"][gender][age_group] = None
                    self.data_manager._save_data()

                print(f"✅ 图片删除成功")
                # 刷新显示
                self.load_style_images(style_id, gender)
                # 使用Windows原生消息框显示成功信息
                import ctypes
                ctypes.windll.user32.MessageBoxW(
                    0,
                    "图片删除成功！",
                    "成功",
                    0 | 64  # MB_OK | MB_ICONINFORMATION
                )
            else:
                QMessageBox.warning(self, "错误", "没有找到要删除的图片！")


class 新版云端风格管理器界面(QMainWindow):
    """新版云端风格管理器主界面 - 图生图模式"""

    style_selected = pyqtSignal(str, str)  # 风格ID, 风格名称

    def __init__(self, parent=None, default_maximized=True):
        super().__init__(parent)
        print(f"🔧 创建新版风格管理器界面，parent={parent}")

        # 设置窗口属性
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint | Qt.WindowMinMaxButtonsHint)
        self.setAttribute(Qt.WA_DeleteOnClose, True)

        self.data_manager = 风格数据管理器()
        self.current_category = None
        self.current_style_id = None
        self.current_gender = "男性"
        self.default_maximized = default_maximized

        self.setup_ui()
        self.load_categories()

        # 直接最大化显示
        if self.default_maximized:
            self.setWindowState(Qt.WindowMaximized)
            self.showMaximized()

    def resizeEvent(self, event):
        """窗口大小变化事件 - 确保图片保持正方形"""
        super().resizeEvent(event)
        # 延迟更新所有图片尺寸
        QTimer.singleShot(100, self._update_all_image_sizes)

    def _update_all_image_sizes(self):
        """更新所有图片尺寸为正方形"""
        # 查找所有SquareImageLabel并更新尺寸
        for widget in self.findChildren(SquareImageLabel):
            if widget.width() > 0:
                widget._update_square_size()

        # 同时调整表格行高
        if hasattr(self, 'image_grid'):
            self.image_grid.adjust_row_height_to_match_width()

    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("🎨 云端风格管理器 - 图生图模式")
        self.setMinimumSize(1400, 900)

        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 设置整体样式
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
                font-size: 16px;
            }
            QLabel {
                color: #333;
                font-size: 16px;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4a90e2, stop:1 #357abd);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 15px 25px;
                font-size: 16px;
                font-weight: 600;
                min-width: 120px;
                min-height: 40px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5ba0f2, stop:1 #4a90e2);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #357abd, stop:1 #2968a3);
            }
            QComboBox {
                background-color: white;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 10px 15px;
                font-size: 16px;
                min-width: 180px;
                min-height: 35px;
            }
            QComboBox:hover {
                border-color: #4a90e2;
            }
            QComboBox::drop-down {
                border: none;
                width: 25px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666;
                margin-right: 6px;
            }
        """)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # 顶部工具栏
        self.create_top_toolbar(main_layout)

        # 主内容区域
        self.create_main_content(main_layout)

        # 状态栏
        self.create_status_bar(main_layout)

    def create_top_toolbar(self, parent_layout):
        """创建顶部工具栏"""
        toolbar_layout = QHBoxLayout()
        toolbar_layout.setSpacing(20)

        # 左侧：分类选择和性别选择
        left_container = QWidget()
        left_container.setStyleSheet("""
            QWidget {
                background-color: white;
                border-radius: 8px;
                padding: 5px;
            }
        """)
        left_layout = QHBoxLayout(left_container)
        left_layout.setContentsMargins(15, 10, 15, 10)
        left_layout.setSpacing(20)

        # 分类选择
        category_label = QLabel("📂 分类:")
        category_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #495057;
            }
        """)
        left_layout.addWidget(category_label)

        self.category_combo = QComboBox()
        self.category_combo.setStyleSheet("""
            QComboBox {
                font-size: 18px;
                font-weight: bold;
                min-width: 200px;
                min-height: 45px;
                padding: 12px 18px;
            }
        """)
        self.category_combo.currentIndexChanged.connect(self.on_category_changed)
        left_layout.addWidget(self.category_combo)

        # 性别选择
        self.gender_selector = 性别选择组件()
        self.gender_selector.gender_changed.connect(self.on_gender_changed)
        left_layout.addWidget(self.gender_selector)

        left_layout.addStretch()
        toolbar_layout.addWidget(left_container)

        # 右侧：操作按钮
        right_container = QWidget()
        right_container.setStyleSheet("""
            QWidget {
                background-color: white;
                border-radius: 8px;
                padding: 5px;
            }
        """)
        right_layout = QHBoxLayout(right_container)
        right_layout.setContentsMargins(15, 10, 15, 10)
        right_layout.setSpacing(10)

        self.add_style_btn = QPushButton("➕ 新增风格")
        self.add_style_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #20a039);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #218838, stop:1 #1e7e34);
            }
        """)
        self.add_style_btn.clicked.connect(self.add_style)
        right_layout.addWidget(self.add_style_btn)

        self.add_category_btn = QPushButton("📁 新增分类")
        self.add_category_btn.clicked.connect(self.add_category)
        right_layout.addWidget(self.add_category_btn)

        toolbar_layout.addWidget(right_container)

        parent_layout.addLayout(toolbar_layout)

    def create_main_content(self, parent_layout):
        """创建主内容区域"""
        content_layout = QHBoxLayout()
        content_layout.setSpacing(10)

        # 左侧：风格导航栏
        self.style_navigator = 风格导航栏()
        self.style_navigator.style_selected.connect(self.on_style_selected)
        content_layout.addWidget(self.style_navigator)

        # 右侧：年龄段图片网格
        self.image_grid = 年龄段图片网格()
        self.image_grid.image_clicked.connect(self.on_image_clicked)
        content_layout.addWidget(self.image_grid)

        parent_layout.addLayout(content_layout)

    def create_status_bar(self, parent_layout):
        """创建状态栏"""
        status_container = QWidget()
        status_container.setStyleSheet("""
            QWidget {
                background-color: white;
                border-radius: 8px;
                border-top: 3px solid #007ACC;
            }
        """)
        status_layout = QHBoxLayout(status_container)
        status_layout.setContentsMargins(20, 10, 20, 10)

        self.status_label = QLabel("✅ 准备就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #495057;
                font-size: 14px;
                font-weight: 500;
            }
        """)
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()

        self.stats_label = QLabel("请选择分类和风格")
        self.stats_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 14px;
            }
        """)
        status_layout.addWidget(self.stats_label)

        parent_layout.addWidget(status_container)



    def load_categories(self):
        """加载分类列表"""
        self.category_combo.clear()
        self.category_combo.addItem("全部", "")

        categories = self.data_manager.get_categories()
        for category in categories:
            self.category_combo.addItem(category, category)

    def on_category_changed(self, index):
        """分类改变事件"""
        category_data = self.category_combo.itemData(index)
        self.current_category = category_data if category_data else None
        print(f"🔄 分类切换到: {self.current_category if self.current_category else '全部'}")

        # 更新风格导航栏
        self.style_navigator.load_styles(self.current_category)

        # 加载所有风格到图片网格 - 多风格表格模式
        self.image_grid.load_all_styles(self.current_category, self.current_gender)

        # 清空当前选中的风格
        self.current_style_id = None

        # 更新状态
        self.update_status()

    def on_gender_changed(self, gender: str):
        """性别改变事件"""
        self.current_gender = gender
        print(f"🔄 性别切换到: {gender}")

        # 重新加载所有风格的图片 - 多风格表格模式
        self.image_grid.load_all_styles(self.current_category, gender)

        self.update_status()

    def on_style_selected(self, style_id: str):
        """风格选择事件 - 实现锚点定位功能"""
        self.current_style_id = style_id
        print(f"🎯 主界面接收到风格选择事件: {style_id}")

        if style_id:
            # 滚动到指定风格位置 - 锚点定位
            print(f"🔄 开始执行滚动到风格: {style_id}")
            self.image_grid.scroll_to_style(style_id)
        else:
            print(f"⚠️ 风格ID为空，跳过滚动")

        # 更新导航栏选中状态
        self.style_navigator.set_selected_style(style_id)

        self.update_status()

    def on_image_clicked(self, style_id: str, gender: str, age_group: str):
        """图片点击事件 - 选择风格参考图"""
        print(f"🖱️ 选择风格参考图: 风格={style_id}, 性别={gender}, 年龄段={age_group}")

        # 获取风格信息
        style, _ = self.data_manager._find_style_by_id(style_id)
        if style:
            # 发射风格选择信号，传递风格名称和详细信息
            style_name = f"{style['name']}_{gender}_{age_group}"
            print(f"📤 发射 style_selected 信号: {style_name}")
            self.style_selected.emit(style_id, style_name)

            # 增加使用次数
            self.data_manager.increment_usage_count(style_id)

            # 关闭窗口
            self.close()

    def update_status(self):
        """更新状态信息"""
        if self.current_style_id:
            style, _ = self.data_manager._find_style_by_id(self.current_style_id)
            if style:
                self.status_label.setText(f"✅ 已选择风格: {style['name']}")
                self.stats_label.setText(f"当前性别: {self.current_gender}")
        elif self.current_category:
            styles = self.data_manager.get_category_styles(self.current_category)
            self.status_label.setText(f"✅ 已加载 {self.current_category} 分类")
            self.stats_label.setText(f"共 {len(styles)} 个风格")
        else:
            styles = self.data_manager.get_all_styles()
            self.status_label.setText("✅ 已加载所有风格")
            self.stats_label.setText(f"共 {len(styles)} 个风格")

    def add_style(self):
        """新增风格"""
        try:
            # 获取当前分类
            current_category = self.category_combo.currentText()
            if not current_category or current_category == "全部":
                QMessageBox.warning(self, "警告", "请先选择一个分类！")
                return

            # 输入风格名称
            from PyQt5.QtWidgets import QInputDialog
            style_name, ok = QInputDialog.getText(
                self,
                "新增风格",
                "请输入风格名称:",
                text=""
            )

            if not ok or not style_name.strip():
                return

            style_name = style_name.strip()

            # 检查风格名称是否已存在
            all_styles = self.data_manager.get_all_styles()
            for style in all_styles:
                if style["name"] == style_name:
                    QMessageBox.warning(self, "警告", f"风格名称 '{style_name}' 已存在！")
                    return

            # 输入风格描述
            description, ok = QInputDialog.getText(
                self,
                "新增风格",
                "请输入风格描述（可选）:",
                text=""
            )

            if not ok:
                description = ""

            # 创建新风格
            success = self.data_manager.add_style(
                category=current_category,
                name=style_name,
                description=description.strip()
            )

            if success:
                # 使用Windows原生消息框显示成功信息
                import ctypes
                ctypes.windll.user32.MessageBoxW(
                    0,
                    f"风格 '{style_name}' 创建成功！",
                    "成功",
                    0 | 64  # MB_OK | MB_ICONINFORMATION
                )

                # 强制重新加载数据管理器
                self.data_manager = 风格数据管理器()
                self.style_navigator.data_manager = 风格数据管理器()
                self.image_grid.data_manager = 风格数据管理器()

                # 刷新界面
                self.load_categories()
                self.style_navigator.load_styles(current_category)

                # 选择新创建的风格
                new_style_id = success  # add_style返回的是style_id
                if new_style_id:
                    self.style_navigator.set_selected_style(new_style_id)
                    self.on_style_selected(new_style_id)

                self.status_label.setText(f"✅ 已创建风格: {style_name}")
            else:
                QMessageBox.critical(self, "错误", "创建风格失败！")

        except Exception as e:
            print(f"❌ 新增风格失败: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"新增风格时发生错误：{str(e)}")

    def add_category(self):
        """新增分类"""
        try:
            # 输入分类名称
            from PyQt5.QtWidgets import QInputDialog
            category_name, ok = QInputDialog.getText(
                self,
                "新增分类",
                "请输入分类名称:",
                text=""
            )

            if not ok or not category_name.strip():
                return

            category_name = category_name.strip()

            # 检查分类名称是否已存在
            categories = self.data_manager.get_categories()
            if category_name in categories:
                QMessageBox.warning(self, "警告", f"分类名称 '{category_name}' 已存在！")
                return

            # 创建新分类
            success = self.data_manager.add_category(category_name)

            if success:
                # 使用Windows原生消息框显示成功信息
                import ctypes
                ctypes.windll.user32.MessageBoxW(
                    0,
                    f"分类 '{category_name}' 创建成功！",
                    "成功",
                    0 | 64  # MB_OK | MB_ICONINFORMATION
                )
                # 刷新界面
                self.load_categories()
                # 选择新创建的分类
                index = self.category_combo.findText(category_name)
                if index >= 0:
                    self.category_combo.setCurrentIndex(index)
                self.status_label.setText(f"✅ 已创建分类: {category_name}")
            else:
                QMessageBox.critical(self, "错误", "创建分类失败！")

        except Exception as e:
            print(f"❌ 新增分类失败: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"新增分类时发生错误：{str(e)}")

    def closeEvent(self, event):
        """关闭事件"""
        print("🔧 新版风格管理器界面正在关闭...")
        event.accept()
        print("✅ 新版风格管理器界面已关闭")
