2025-08-01 08:26:15 - DEBUG - A0_config-390: 启用离线模式

2025-08-01 08:26:15 - DEBUG - A0_config-618: [调试] 离线模式已启用，跳过VIP检查

2025-08-01 08:26:15 - DEBUG - A0_config-763: [调试] 正在保存平台配置到: C:\Users\<USER>\Desktop\纯净wen\wen整合 - 副本 - 副本\config\platforms.json

2025-08-01 08:26:15 - DEBUG - A0_config-766: [调试] 平台配置保存成功

2025-08-01 08:26:15 - DEBUG - A0_start_task-35: 基础模块导入成功

2025-08-01 08:26:15 - DEBUG - A0_start_task-236: 192GB内存优化环境变量已设置

2025-08-01 08:26:15 - DEBUG - A30_模型内存管理-27: 🚀 模型内存管理器启动 - 最大缓存: 64.0GB

2025-08-01 08:26:15 - DEBUG - A0_start_task-242: 模型内存管理器已启动

2025-08-01 08:26:20 - DEBUG - A0_start_task-187: 已成功修补A4_working.启动程序函数

2025-08-01 08:26:20 - DEBUG - A0_start_task-190: 批量任务模块修复已准备好

2025-08-01 08:26:20 - DEBUG - A1_注册账号-102: DEBUG: MainWindow __init__ start

2025-08-01 08:26:20 - DEBUG - A1_注册账号-318: 离线模式，跳过用户状态查询。

2025-08-01 08:26:21 - DEBUG - A1_注册账号-182: 检测到离线模式，将直接启动主程序。

2025-08-01 08:26:21 - DEBUG - A0_start_task-216: 跳过更新检查

2025-08-01 08:26:21 - DEBUG - A1_注册账号-230: 已修复: A0_config.字体色

2025-08-01 08:26:21 - DEBUG - main_window-248: 界面加载完成

2025-08-01 08:26:21 - DEBUG - main_window-371: 🔄 浏览器绘图方案未启用，使用默认绘图流程

2025-08-01 08:26:25 - DEBUG - path_manager-240: ℹ️ 配置文件已使用相对路径，无需迁移: C:\Users\<USER>\Desktop\纯净wen\wen整合 - 副本 - 副本\项目文件\原创项目\蜂蝶随香\蜂蝶随香.json

2025-08-01 08:26:25 - DEBUG - main_window-2152: [调试] 开始修复所有备选图片数据...

2025-08-01 08:26:25 - DEBUG - main_window-2166: [调试] 所有备选图片数据完整，无需修复

2025-08-01 08:26:25 - DEBUG - main_window-3741: 加载项目任务

2025-08-01 08:26:25 - DEBUG - main_window-307: 已加载角色专用种子配置，包含 3 个角色:

2025-08-01 08:26:25 - DEBUG - main_window-312:   - 艾米丽: seed=1721289378 (禁用, 更新时间: 2025-07-01 09:30:00)

2025-08-01 08:26:25 - DEBUG - main_window-312:   - 露西: seed=2847563921 (禁用, 更新时间: 2025-07-01 09:30:00)

2025-08-01 08:26:25 - DEBUG - main_window-312:   - 斑斑: seed=3956842107 (禁用, 更新时间: 2025-07-01 09:30:00)

2025-08-01 08:26:25 - DEBUG - main_window-3935: 播放任务完成提示音

2025-08-01 08:26:25 - DEBUG - main_window-4997: 加载项目任务完成: 蜂蝶随香

2025-08-01 08:26:27 - DEBUG - 批量任务-44: 用时: 2 秒

2025-08-01 08:26:29 - DEBUG - main_window-3935: 播放任务完成提示音

2025-08-01 08:26:31 - DEBUG - A4_人物设定-1676: 🎯 标签页 全局 风格按钮显示更新为: '🌐 经典水彩_男性_青年'

2025-08-01 08:26:31 - DEBUG - A4_人物设定-1474: ✅ 已为标签页[全局]创建云端风格控件

2025-08-01 08:26:31 - DEBUG - A4_人物设定-554: ✅ 拼接按钮事件已连接 - tab: 全局

2025-08-01 08:26:31 - DEBUG - A4_人物设定-1676: 🎯 标签页 艾米丽 风格按钮显示更新为: '🌐 经典水彩_男性_青年'

2025-08-01 08:26:31 - DEBUG - A4_人物设定-1474: ✅ 已为标签页[艾米丽]创建云端风格控件

2025-08-01 08:26:31 - DEBUG - A4_人物设定-554: ✅ 拼接按钮事件已连接 - tab: 艾米丽

2025-08-01 08:26:31 - DEBUG - A4_人物设定-1676: 🎯 标签页 露西 风格按钮显示更新为: '🌐 经典水彩_男性_青年'

2025-08-01 08:26:31 - DEBUG - A4_人物设定-1474: ✅ 已为标签页[露西]创建云端风格控件

2025-08-01 08:26:31 - DEBUG - A4_人物设定-554: ✅ 拼接按钮事件已连接 - tab: 露西

2025-08-01 08:26:31 - DEBUG - A4_人物设定-1676: 🎯 标签页 斑斑 风格按钮显示更新为: '🌐 经典水彩_男性_青年'

2025-08-01 08:26:32 - DEBUG - A4_人物设定-1474: ✅ 已为标签页[斑斑]创建云端风格控件

2025-08-01 08:26:32 - DEBUG - A4_人物设定-554: ✅ 拼接按钮事件已连接 - tab: 斑斑

2025-08-01 08:26:34 - DEBUG - A4_人物设定-1461: 🎯 云端风格按钮被点击 - 标签页: 艾米丽

2025-08-01 08:26:34 - DEBUG - A4_人物设定-1591: 🎯 打开云端风格管理器被调用 - 标签页: 艾米丽

2025-08-01 08:26:34 - DEBUG - 新版风格管理界面-960: 🔧 创建新版风格管理器界面，parent=None

2025-08-01 08:26:34 - DEBUG - 新版风格管理界面-1228: 🔄 分类切换到: 全部

2025-08-01 08:26:36 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 798ac6e5

2025-08-01 08:26:36 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 13

2025-08-01 08:26:38 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 6f36e093

2025-08-01 08:26:38 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 10

2025-08-01 08:26:39 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 512b709e

2025-08-01 08:26:39 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 6

2025-08-01 08:26:40 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 031c72cf

2025-08-01 08:26:40 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 4

2025-08-01 08:26:41 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 2d6bdc42

2025-08-01 08:26:41 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 2

2025-08-01 08:26:41 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: cc810025

2025-08-01 08:26:41 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 0

2025-08-01 08:26:43 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: d8f2abda

2025-08-01 08:26:43 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 1

2025-08-01 08:26:45 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 2d6bdc42

2025-08-01 08:26:45 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 2

2025-08-01 08:26:46 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 5f1603e6

2025-08-01 08:26:46 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 3

2025-08-01 08:26:46 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 031c72cf

2025-08-01 08:26:46 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 4

2025-08-01 08:26:47 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: a6c388e8

2025-08-01 08:26:47 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 5

2025-08-01 08:26:47 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 512b709e

2025-08-01 08:26:47 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 6

2025-08-01 08:26:48 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 439a680a

2025-08-01 08:26:48 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 7

2025-08-01 08:26:48 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 858f2f2b

2025-08-01 08:26:48 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 9

2025-08-01 08:26:49 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 2f00ec7a

2025-08-01 08:26:49 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 12

2025-08-01 08:26:51 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 6f36e093

2025-08-01 08:26:51 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 10

2025-08-01 08:26:51 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 858f2f2b

2025-08-01 08:26:51 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 9

2025-08-01 08:26:52 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 363b5462

2025-08-01 08:26:52 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 8

2025-08-01 08:26:53 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 858f2f2b

2025-08-01 08:26:53 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 9

2025-08-01 08:26:53 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 6f36e093

2025-08-01 08:26:53 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 10

2025-08-01 08:26:54 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: dab1cd89

2025-08-01 08:26:54 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 11

2025-08-01 08:26:55 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 2f00ec7a

2025-08-01 08:26:55 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 12

2025-08-01 08:26:56 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: c8f4e4ca

2025-08-01 08:26:56 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 14

2025-08-01 08:26:57 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 798ac6e5

2025-08-01 08:26:57 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 13

2025-08-01 08:26:58 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: c8f4e4ca

2025-08-01 08:26:58 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 14

2025-08-01 08:26:59 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 798ac6e5

2025-08-01 08:26:59 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 13

2025-08-01 08:27:00 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 2f00ec7a

2025-08-01 08:27:00 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 12

2025-08-01 08:27:01 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 6f36e093

2025-08-01 08:27:01 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 10

2025-08-01 08:27:03 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 858f2f2b

2025-08-01 08:27:03 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 9

2025-08-01 08:27:04 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 363b5462

2025-08-01 08:27:04 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 8

2025-08-01 08:27:04 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 439a680a

2025-08-01 08:27:04 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 7

2025-08-01 08:27:05 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: a6c388e8

2025-08-01 08:27:05 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 5

2025-08-01 08:27:05 - DEBUG - 新版风格管理界面-1255: 🎯 选择风格: 031c72cf

2025-08-01 08:27:05 - DEBUG - 新版风格管理界面-599: 🎯 滚动到行: 4

