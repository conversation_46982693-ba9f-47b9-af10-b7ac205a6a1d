#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试滚动功能验证

这个脚本专门测试滚动计算和执行是否正确
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QHBoxLayout
from PyQt5.QtCore import QTimer

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_scroll_functionality():
    """直接测试滚动功能"""
    print("🚀 启动滚动功能直接验证测试...")
    
    app = QApplication(sys.argv)
    
    try:
        # 导入修复后的界面
        from pyz.工作流.云端风格管理器.新版风格管理界面 import 新版云端风格管理器界面
        
        # 创建主界面
        window = 新版云端风格管理器界面()
        
        # 创建测试控制面板
        test_panel = QWidget()
        test_layout = QVBoxLayout(test_panel)
        
        # 添加测试按钮
        test_buttons_layout = QHBoxLayout()
        
        def test_scroll_to_row(row_num):
            """测试滚动到指定行"""
            print(f"\n🎯 测试滚动到第 {row_num} 行")
            if hasattr(window.image_grid, 'grid_table'):
                table = window.image_grid.grid_table
                print(f"📊 表格状态: 行数={table.rowCount()}, 列数={table.columnCount()}")
                
                # 打印所有行的高度
                for i in range(table.rowCount()):
                    height = table.rowHeight(i)
                    print(f"📏 行 {i} 高度: {height}px")
                
                # 直接调用滚动方法
                if row_num < table.rowCount():
                    window.image_grid._perform_scroll_to_row(row_num)
                else:
                    print(f"❌ 行号 {row_num} 超出范围")
            else:
                print(f"❌ 表格未初始化")
        
        # 创建测试按钮
        for i in range(5):
            btn = QPushButton(f"滚动到第{i}行")
            btn.clicked.connect(lambda checked, row=i: test_scroll_to_row(row))
            test_buttons_layout.addWidget(btn)
        
        test_layout.addLayout(test_buttons_layout)
        
        # 添加滚动条状态显示
        def show_scrollbar_status():
            """显示滚动条状态"""
            if hasattr(window.image_grid, 'grid_table'):
                table = window.image_grid.grid_table
                v_scrollbar = table.verticalScrollBar()
                if v_scrollbar:
                    print(f"📊 滚动条状态:")
                    print(f"   当前值: {v_scrollbar.value()}")
                    print(f"   最小值: {v_scrollbar.minimum()}")
                    print(f"   最大值: {v_scrollbar.maximum()}")
                    print(f"   页面大小: {v_scrollbar.pageStep()}")
                    print(f"   单步大小: {v_scrollbar.singleStep()}")
                    print(f"   是否可见: {v_scrollbar.isVisible()}")
                else:
                    print(f"❌ 无法获取滚动条")
            else:
                print(f"❌ 表格未初始化")
        
        status_btn = QPushButton("显示滚动条状态")
        status_btn.clicked.connect(show_scrollbar_status)
        test_layout.addWidget(status_btn)
        
        # 添加手动滚动测试
        def manual_scroll_test():
            """手动滚动测试"""
            if hasattr(window.image_grid, 'grid_table'):
                table = window.image_grid.grid_table
                v_scrollbar = table.verticalScrollBar()
                if v_scrollbar:
                    # 滚动到中间位置
                    middle_pos = (v_scrollbar.maximum() - v_scrollbar.minimum()) // 2
                    print(f"🔄 手动滚动到中间位置: {middle_pos}")
                    v_scrollbar.setValue(middle_pos)
                    
                    # 验证滚动结果
                    QTimer.singleShot(100, lambda: print(f"📊 滚动后位置: {v_scrollbar.value()}"))
        
        manual_btn = QPushButton("手动滚动测试")
        manual_btn.clicked.connect(manual_scroll_test)
        test_layout.addWidget(manual_btn)
        
        # 设置测试面板
        test_panel.setWindowTitle("滚动功能测试控制面板")
        test_panel.resize(600, 200)
        test_panel.show()
        
        # 显示主界面
        window.show()
        
        print("✅ 测试界面启动成功！")
        print("\n📝 测试说明:")
        print("1. 等待主界面完全加载")
        print("2. 点击测试控制面板中的按钮")
        print("3. 观察主界面的滚动效果")
        print("4. 查看控制台的详细输出")
        
        print("\n🎯 测试重点:")
        print("- 行高是否正确设置")
        print("- 滚动位置计算是否准确")
        print("- 滚动条是否正确响应")
        print("- 目标行是否正确高亮")
        
        # 启动应用
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🔧 滚动功能直接验证测试")
    print("=" * 60)
    
    test_scroll_functionality()
