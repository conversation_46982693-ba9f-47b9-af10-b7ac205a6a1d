{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "broken_until": "1752899095", "host": "android.clients.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "broken_count": 1, "broken_until": "**********", "host": "optimizationguide-pa.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3hwb2V0LmNu", false, 0], "broken_count": 1, "broken_until": "**********", "host": "content-autofill.googleapis.com", "port": 443, "protocol_str": "quic"}], "servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://play.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://ogads-pa.clients6.google.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3hwb2V0LmNu", false, 0], "server": "https://api.ipify.org", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3hwb2V0LmNu", false, 0], "server": "https://busuanzi.ibruce.info", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3hwb2V0LmNu", false, 0], "server": "https://api.github.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3hwb2V0LmNu", false, 0], "server": "https://ipapi.co", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399964404312984", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3hwb2V0LmNu", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3hwb2V0LmNu", false, 0], "server": "https://picx.xpoet.cn", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399964418145354", "port": 443, "protocol_str": "quic"}], "anonymization": ["IAAAABoAAABodHRwczovL3d3dy5nb29nbGVhcGlzLmNvbQAA", false, 0], "server": "https://www.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399964459311897", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399964459797312", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "server": "https://beacons.gcp.gvt2.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399964460225986", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://apis.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dpdGh1Yi5jb20AAA==", false, 0], "server": "https://github.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dpdGh1Yi5jb20AAA==", false, 0], "server": "https://github.githubassets.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dpdGh1Yi5jb20AAA==", false, 0], "server": "https://collector.github.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399964465132455", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dpdGh1Yi5jb20AAA==", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399964477494142", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://android.clients.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399964389393520", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dpdGh1Yi5jb20AAA==", false, 0], "server": "https://api.github.com", "supports_spdy": true}], "version": 5}, "network_qualities": {"CAESABiAgICA+P////8B": "4G"}}}