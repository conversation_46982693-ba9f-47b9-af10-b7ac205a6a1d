{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["FAAAABAAAABodHRwczovL3hwb2V0LmNu", false, 0], "broken_count": 1, "broken_until": "**********", "host": "content-autofill.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "broken_until": "**********", "host": "android.clients.google.com", "port": 443, "protocol_str": "quic"}], "servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.google.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3hwb2V0LmNu", false, 0], "server": "https://api.ipify.org", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3hwb2V0LmNu", false, 0], "server": "https://api.github.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3hwb2V0LmNu", false, 0], "server": "https://busuanzi.ibruce.info", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3hwb2V0LmNu", false, 0], "server": "https://ipapi.co", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3hwb2V0LmNu", false, 0], "server": "https://picx.xpoet.cn", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399962175055235", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3hwb2V0LmNu", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399962176111374", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://android.clients.google.com", "supports_spdy": true}], "version": 5}, "network_qualities": {"CAESABiAgICA+P////8B": "4G"}}}